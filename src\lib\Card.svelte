<script lang="ts">
	import CardSide from './CardSide.svelte';

	let {
		widthPx = 180,
		aspectRatio = 2 / 2.8,
		isFrontVisible
	}: { widthPx?: number; aspectRatio?: number; isFrontVisible?: boolean } = $props();
</script>

<div
	class="relative touch-pinch-zoom bg-transparent select-none perspective-[100vw] perspective-origin-center"
	style="width: {widthPx}px; height: 220px; aspect-ratio: {aspectRatio}"
>
	<div class="card-content {isFrontVisible ? 'is-front-visible' : ''}">
		<!-- Back side -->
		<div class="absolute inset-0">
			<CardSide></CardSide>
		</div>

		<!-- Front side -->
		<div class="absolute inset-0">
			<CardSide></CardSide>
		</div>
	</div>
</div>

<style>
	.card-content {
		transition: transform 0.6s cubic-bezier(0.12, 0, 0.39, 0);
		transform-style: preserve-3d;
	}

	.card-content.is-front-visible {
		transform: rotateY(180deg);
	}
</style>
